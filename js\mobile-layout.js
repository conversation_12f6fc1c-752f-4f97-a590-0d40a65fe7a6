// Mobile Layout Adjustments
// - Moves primary hero buttons under the visual on mobile
// - Overlays the team credentials on the bottom-right of the team image on mobile

document.addEventListener("DOMContentLoaded", function () {
  function adjustMobileLayout() {
    const heroContent = document.querySelector(".hero-content");
    const heroActions = document.querySelector(".hero-actions");
    const heroVisual = document.querySelector(".hero-visual");

    if (heroContent && heroActions && heroVisual) {
      // Always clean up existing mobile actions first
      const existingMobileActions = document.querySelector(".mobile-actions");
      if (existingMobileActions) existingMobileActions.remove();

      if (window.innerWidth <= 768) {
        // Clone the buttons with deep clone to preserve all content
        const actionsClone = heroActions.cloneNode(true);
        actionsClone.classList.add("mobile-actions");

        // Ensure all button content is preserved
        const originalButtons = heroActions.querySelectorAll(".btn");
        const clonedButtons = actionsClone.querySelectorAll(".btn");
        originalButtons.forEach((originalBtn, index) => {
          if (clonedButtons[index]) {
            clonedButtons[index].innerHTML = originalBtn.innerHTML;
            clonedButtons[index].className = originalBtn.className;
            clonedButtons[index].href = originalBtn.href;
            if (originalBtn.onclick)
              clonedButtons[index].onclick = originalBtn.onclick;
          }
        });

        // Hide original buttons and insert mobile clone after the visual
        heroActions.style.display = "none";
        heroVisual.parentNode.insertBefore(
          actionsClone,
          heroVisual.nextSibling
        );
      } else {
        // Desktop - restore original layout
        heroActions.style.display = "";
      }
    }
  }

  function adjustTeamCredentialsOverlay() {
    // Credentials are now shown in main layout on mobile, no overlay needed
    const imageContainer = document.querySelector(".team-image-container");
    if (!imageContainer) return;

    // Remove any existing overlay
    const overlay = imageContainer.querySelector(".credentials-overlay");
    if (overlay) overlay.remove();

    // Ensure original credentials are always visible
    const originalCreds = document.querySelector(".team-credentials");
    if (originalCreds) {
      originalCreds.style.display = "";
    }
  }

  function adjustContactPageLayout() {
    // Only run on contact page
    if (!window.location.pathname.includes("contact.html")) return;

    const contactSection = document.querySelector(
      'section[style*="grid-template-columns: 1fr 1fr"]'
    );
    if (!contactSection) return;

    const container = contactSection.querySelector(".container > div");
    if (!container) return;

    const contactInfo = container.children[0]; // Contact Information div
    const contactForm = container.children[1]; // Contact Form div

    if (!contactInfo || !contactForm) return;

    if (window.innerWidth <= 768) {
      // Mobile layout: Form after title/description, then contact details, then buttons
      const titleSection = contactInfo.children[0]; // Title and description
      const detailsSection = contactInfo.children[1]; // Contact details
      const buttonsSection = contactInfo.children[2]; // Buttons

      // Reorganize order: title, form, details, buttons
      container.style.display = "flex";
      container.style.flexDirection = "column";
      container.style.gap = "40px";

      // Set order
      titleSection.style.order = "1";
      contactForm.style.order = "2";
      detailsSection.style.order = "3";
      buttonsSection.style.order = "4";
    } else {
      // Desktop layout: restore original
      container.style.display = "";
      container.style.flexDirection = "";
      container.style.gap = "";

      // Reset order
      const elements = [
        contactInfo.children[0],
        contactInfo.children[1],
        contactInfo.children[2],
        contactForm,
      ];
      elements.forEach((el) => {
        if (el) el.style.order = "";
      });
    }
  }

  function adjustWhyChooseUsLayout() {
    // Only run on homepage
    if (
      !window.location.pathname.includes("index.html") &&
      window.location.pathname !== "/"
    )
      return;

    const whyChooseSection = document.querySelector(".why-choose-us");
    if (!whyChooseSection) return;

    const contentSplit = whyChooseSection.querySelector(".content-split");
    const contentLeft = whyChooseSection.querySelector(".content-left");
    const contentRight = whyChooseSection.querySelector(".content-right");
    const title = contentLeft?.querySelector("h2");
    const features = contentLeft?.querySelector(".features");

    if (!contentSplit || !contentLeft || !contentRight || !title || !features)
      return;

    if (window.innerWidth <= 768) {
      // Mobile layout: Title, Image, Features
      contentSplit.style.display = "flex";
      contentSplit.style.flexDirection = "column";
      contentSplit.style.gap = "2rem";
      contentSplit.style.textAlign = "center";

      // Set order: title first, image second, features third
      title.style.order = "1";
      title.style.marginBottom = "2rem";

      contentRight.style.order = "2";
      contentRight.style.display = "flex";
      contentRight.style.justifyContent = "center";

      features.style.order = "3";
      features.style.marginTop = "2rem";
    } else {
      // Desktop layout: restore original
      contentSplit.style.display = "";
      contentSplit.style.flexDirection = "";
      contentSplit.style.gap = "";
      contentSplit.style.textAlign = "";

      // Reset order
      title.style.order = "";
      title.style.marginBottom = "";
      contentRight.style.order = "";
      contentRight.style.display = "";
      contentRight.style.justifyContent = "";
      features.style.order = "";
      features.style.marginTop = "";
    }
  }

  // Run on load
  adjustMobileLayout();
  adjustTeamCredentialsOverlay();
  adjustContactPageLayout();
  adjustWhyChooseUsLayout();

  // Run on resize with debounce
  let resizeTimer;
  window.addEventListener("resize", function () {
    clearTimeout(resizeTimer);
    resizeTimer = setTimeout(function () {
      adjustMobileLayout();
      adjustTeamCredentialsOverlay();
      adjustContactPageLayout();
      adjustWhyChooseUsLayout();
    }, 250);
  });
});
